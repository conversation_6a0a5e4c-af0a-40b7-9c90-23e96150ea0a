package com.winit.workflow.engine.core.extension.camunda.parser;

import com.alibaba.smart.framework.engine.bpmn.assembly.extension.ExtensionElementsImpl;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElements;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.winit.workflow.engine.core.api.ExtensionElementsConstant;
import com.winit.workflow.engine.core.extension.camunda.model.*;
import org.junit.Test;

import java.util.Map;

import static org.junit.Assert.*;

/**
 * Camunda 扩展元素使用示例
 * 展示如何创建和使用Camunda扩展元素
 *
 * <AUTHOR> Team
 */
public class CamundaExtensionUsageExample {

    @Test
    public void demonstrateCamundaExtensionUsage() {
        // 1. 创建Camunda InputOutput扩展
        CamundaInputOutput inputOutput = createSampleInputOutput();

        // 2. 将扩展添加到ExtensionElements中
        ExtensionElements extensionElements = new ExtensionElementsImpl();
        ParseContext context = new ParseContext();

        // 装饰扩展元素
        inputOutput.decorate(extensionElements, context);

        // 3. 验证扩展数据已正确存储
        Map<String, Object> decorationMap = extensionElements.getDecorationMap();
        assertTrue(decorationMap.containsKey(ExtensionElementsConstant.CAMUNDA_INPUT_OUTPUT));

        @SuppressWarnings("unchecked")
        Map<String, Object> camundaData = (Map<String, Object>) decorationMap.get(ExtensionElementsConstant.CAMUNDA_INPUT_OUTPUT);

        // 验证输入参数
        @SuppressWarnings("unchecked")
        Map<String, Object> inputParams = (Map<String, Object>) camundaData.get("inputParameters");
        assertNotNull(inputParams);
        assertEquals("simpleValue", inputParams.get("simpleInput"));

        // 验证输出参数
        @SuppressWarnings("unchecked")
        Map<String, Object> outputParams = (Map<String, Object>) camundaData.get("outputParameters");
        assertNotNull(outputParams);
        assertEquals("outputValue", outputParams.get("simpleOutput"));

        System.out.println("Camunda扩展元素使用示例执行成功！");
        System.out.println("输入参数: " + inputParams);
        System.out.println("输出参数: " + outputParams);
    }

    /**
     * 创建示例InputOutput配置
     * 模拟以下XML结构：
     * <camunda:inputOutput>
     * <camunda:inputParameter name="simpleInput">simpleValue</camunda:inputParameter>
     * <camunda:inputParameter name="mapInput">
     * <camunda:map>
     * <camunda:entry key="key1">value1</camunda:entry>
     * <camunda:entry key="key2">value2</camunda:entry>
     * </camunda:map>
     * </camunda:inputParameter>
     * <camunda:outputParameter name="simpleOutput">outputValue</camunda:outputParameter>
     * <camunda:outputParameter name="mapOutput">
     * <camunda:map>
     * <camunda:entry key="outKey1">outValue1</camunda:entry>
     * <camunda:entry key="outKey2">outValue2</camunda:entry>
     * </camunda:map>
     * </camunda:outputParameter>
     * </camunda:inputOutput>
     */
    private CamundaInputOutput createSampleInputOutput() {
        CamundaInputOutput inputOutput = new CamundaInputOutput();

        // 创建简单输入参数
        CamundaInputParameter simpleInput = new CamundaInputParameter();
        simpleInput.setName("simpleInput");
        simpleInput.setStringValue("simpleValue");

        // 创建Map类型输入参数
        CamundaInputParameter mapInput = new CamundaInputParameter();
        mapInput.setName("mapInput");

        CamundaMap inputMap = new CamundaMap();
        inputMap.addEntry(new CamundaEntry("key1", "value1"));
        inputMap.addEntry(new CamundaEntry("key2", "value2"));
        mapInput.setMapValue(inputMap);

        // 创建简单输出参数
        CamundaOutputParameter simpleOutput = new CamundaOutputParameter();
        simpleOutput.setName("simpleOutput");
        simpleOutput.setStringValue("outputValue");

        // 创建Map类型输出参数
        CamundaOutputParameter mapOutput = new CamundaOutputParameter();
        mapOutput.setName("mapOutput");

        CamundaMap outputMap = new CamundaMap();
        outputMap.addEntry(new CamundaEntry("outKey1", "outValue1"));
        outputMap.addEntry(new CamundaEntry("outKey2", "outValue2"));
        mapOutput.setMapValue(outputMap);

        // 添加到InputOutput
        inputOutput.addInputParameter(simpleInput);
        inputOutput.addInputParameter(mapInput);
        inputOutput.addOutputParameter(simpleOutput);
        inputOutput.addOutputParameter(mapOutput);

        return inputOutput;
    }

    @Test
    public void demonstrateParameterTypes() {
        System.out.println("\n=== Camunda参数类型示例 ===");

        // 字符串类型参数
        CamundaInputParameter stringParam = new CamundaInputParameter();
        stringParam.setName("stringParam");
        stringParam.setStringValue("Hello World");

        System.out.println("字符串参数: " + stringParam.getName() + " = " + stringParam.getStringValue());
        System.out.println("是否为Map类型: " + stringParam.isMapType());

        // Map类型参数
        CamundaInputParameter mapParam = new CamundaInputParameter();
        mapParam.setName("mapParam");

        CamundaMap map = new CamundaMap();
        map.addEntry(new CamundaEntry("database", "mysql"));
        map.addEntry(new CamundaEntry("port", "3306"));
        map.addEntry(new CamundaEntry("host", "localhost"));
        mapParam.setMapValue(map);

        System.out.println("Map参数: " + mapParam.getName());
        System.out.println("是否为Map类型: " + mapParam.isMapType());
        System.out.println("Map内容: " + mapParam.getMapValue().toMap());

        // 验证
        assertEquals("Hello World", stringParam.getStringValue());
        assertFalse(stringParam.isMapType());

        assertTrue(mapParam.isMapType());
        assertEquals("mysql", mapParam.getMapValue().getValue("database"));
        assertEquals("3306", mapParam.getMapValue().getValue("port"));
        assertEquals("localhost", mapParam.getMapValue().getValue("host"));
    }
}
