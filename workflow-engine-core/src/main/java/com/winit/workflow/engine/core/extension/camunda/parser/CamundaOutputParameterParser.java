package com.alibaba.smart.framework.engine.bpmn.assembly.extension.parser.camunda;

import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;

import com.alibaba.smart.framework.engine.bpmn.assembly.extension.camunda.CamundaOutputParameter;
import com.alibaba.smart.framework.engine.bpmn.assembly.extension.camunda.CamundaMap;
import com.alibaba.smart.framework.engine.exception.EngineException;
import com.alibaba.smart.framework.engine.extension.annoation.ExtensionBinding;
import com.alibaba.smart.framework.engine.extension.constant.ExtensionConstant;
import com.alibaba.smart.framework.engine.model.assembly.BaseElement;
import com.alibaba.smart.framework.engine.xml.parser.AbstractElementParser;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.alibaba.smart.framework.engine.xml.util.XmlParseUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Camunda OutputParameter 解析器
 * 用于解析 camunda:outputParameter 元素
 * 
 * <AUTHOR> Team
 */
@ExtensionBinding(group = ExtensionConstant.ELEMENT_PARSER, bindKey = CamundaOutputParameter.class)
public class CamundaOutputParameterParser extends AbstractElementParser<CamundaOutputParameter> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CamundaOutputParameterParser.class);

    @Override
    protected CamundaOutputParameter parseModel(XMLStreamReader reader, ParseContext context) {
        CamundaOutputParameter outputParameter = new CamundaOutputParameter();
        
        // 解析name属性
        String name = XmlParseUtil.getString(reader, "name");
        outputParameter.setName(name);
        
        LOGGER.debug("开始解析 camunda:outputParameter，name: {}", name);
        
        return outputParameter;
    }

    @Override
    protected void decorateChild(CamundaOutputParameter outputParameter, BaseElement child, ParseContext context) {
        if (child instanceof CamundaMap) {
            // 如果子元素是Map，设置为Map值
            outputParameter.setMapValue((CamundaMap) child);
            LOGGER.debug("设置输出参数 {} 的Map值", outputParameter.getName());
        } else {
            LOGGER.warn("未知的子元素类型: {}", child.getClass().getName());
            throw new EngineException("CamundaOutputParameter 只能包含 CamundaMap 子元素: " + child.getClass());
        }
    }

    @Override
    public CamundaOutputParameter parseElement(XMLStreamReader reader, ParseContext context) throws XMLStreamException {
        CamundaOutputParameter outputParameter = parseModel(reader, context);

        context = context.evolve(outputParameter);

        // 使用标准的解析方式
        boolean hasChildren = false;

        // 检查是否有子元素
        while (XmlParseUtil.nextChildElement(reader)) {
            hasChildren = true;
            Object element = this.readElement(reader, context);
            if (element instanceof BaseElement) {
                this.decorateChild(outputParameter, (BaseElement) element, context);
            }
        }

        // 如果没有子元素，尝试读取文本内容
        if (!hasChildren) {
            try {
                String text = reader.getElementText();
                if (text != null && !text.trim().isEmpty()) {
                    outputParameter.setStringValue(text.trim());
                    LOGGER.debug("设置输出参数 {} 的字符串值: {}", outputParameter.getName(), text.trim());
                }
            } catch (Exception e) {
                LOGGER.debug("读取输出参数文本内容失败: {}", e.getMessage());
            }
        }

        return outputParameter;
    }

    @Override
    public Class<CamundaOutputParameter> getModelType() {
        return CamundaOutputParameter.class;
    }
}
