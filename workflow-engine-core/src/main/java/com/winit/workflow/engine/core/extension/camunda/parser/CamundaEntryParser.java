package com.alibaba.smart.framework.engine.bpmn.assembly.extension.parser.camunda;

import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;

import com.alibaba.smart.framework.engine.bpmn.assembly.extension.camunda.CamundaEntry;
import com.alibaba.smart.framework.engine.extension.annoation.ExtensionBinding;
import com.alibaba.smart.framework.engine.extension.constant.ExtensionConstant;
import com.alibaba.smart.framework.engine.xml.parser.AbstractElementParser;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.alibaba.smart.framework.engine.xml.util.XmlParseUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Camunda Entry 解析器
 * 用于解析 camunda:entry 元素
 * 
 * <AUTHOR> Team
 */
@ExtensionBinding(group = ExtensionConstant.ELEMENT_PARSER, bindKey = CamundaEntry.class)
public class CamundaEntryParser extends AbstractElementParser<CamundaEntry> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CamundaEntryParser.class);

    @Override
    protected CamundaEntry parseModel(XMLStreamReader reader, ParseContext context) {
        CamundaEntry entry = new CamundaEntry();
        
        // 解析key属性
        String key = XmlParseUtil.getString(reader, "key");
        entry.setKey(key);
        
        LOGGER.debug("开始解析 camunda:entry，key: {}", key);
        
        return entry;
    }

    @Override
    public CamundaEntry parseElement(XMLStreamReader reader, ParseContext context) throws XMLStreamException {
        CamundaEntry entry = parseModel(reader, context);
        
        // 读取元素的文本内容作为value
        try {
            String textContent = reader.getElementText();
            if (textContent != null) {
                entry.setValue(textContent.trim());
                LOGGER.debug("设置Entry值: key={}, value={}", entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            LOGGER.warn("读取Entry文本内容失败: {}", e.getMessage());
        }
        
        return entry;
    }

    @Override
    public Class<CamundaEntry> getModelType() {
        return CamundaEntry.class;
    }
}
