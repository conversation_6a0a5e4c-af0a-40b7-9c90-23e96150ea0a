package com.winit.workflow.engine.core.extension.camunda.parser;

import com.alibaba.smart.framework.engine.exception.EngineException;
import com.alibaba.smart.framework.engine.extension.annoation.ExtensionBinding;
import com.alibaba.smart.framework.engine.extension.constant.ExtensionConstant;
import com.alibaba.smart.framework.engine.model.assembly.BaseElement;
import com.alibaba.smart.framework.engine.xml.parser.AbstractElementParser;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.alibaba.smart.framework.engine.xml.util.XmlParseUtil;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaInputParameter;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;

/**
 * Camunda InputParameter 解析器
 * 用于解析 camunda:inputParameter 元素
 *
 * <AUTHOR> Team
 */
@ExtensionBinding(group = ExtensionConstant.ELEMENT_PARSER, bindKey = CamundaInputParameter.class)
public class CamundaInputParameterParser extends AbstractElementParser<CamundaInputParameter> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CamundaInputParameterParser.class);

    @Override
    protected CamundaInputParameter parseModel(XMLStreamReader reader, ParseContext context) {
        CamundaInputParameter inputParameter = new CamundaInputParameter();

        // 解析name属性
        String name = XmlParseUtil.getString(reader, "name");
        inputParameter.setName(name);

        LOGGER.debug("开始解析 camunda:inputParameter，name: {}", name);

        return inputParameter;
    }

    @Override
    protected void decorateChild(CamundaInputParameter inputParameter, BaseElement child, ParseContext context) {
        if (child instanceof CamundaMap) {
            // 如果子元素是Map，设置为Map值
            inputParameter.setMapValue((CamundaMap) child);
            LOGGER.debug("设置输入参数 {} 的Map值", inputParameter.getName());
        } else {
            LOGGER.warn("未知的子元素类型: {}", child.getClass().getName());
            throw new EngineException("CamundaInputParameter can only contain CamundaMap child elements: " + child.getClass());
        }
    }

    @Override
    public CamundaInputParameter parseElement(XMLStreamReader reader, ParseContext context) throws XMLStreamException {
        CamundaInputParameter inputParameter = parseModel(reader, context);

        context = context.evolve(inputParameter);

        // 使用标准的解析方式
        StringBuilder textContent = new StringBuilder();
        boolean hasChildren = false;

        // 检查是否有子元素
        while (XmlParseUtil.nextChildElement(reader)) {
            hasChildren = true;
            Object element = this.readElement(reader, context);
            if (element instanceof BaseElement) {
                this.decorateChild(inputParameter, (BaseElement) element, context);
            }
        }

        // 如果没有子元素，尝试读取文本内容
        if (!hasChildren) {
            try {
                String text = reader.getElementText();
                if (text != null && !text.trim().isEmpty()) {
                    inputParameter.setStringValue(text.trim());
                    LOGGER.debug("设置输入参数 {} 的字符串值: {}", inputParameter.getName(), text.trim());
                }
            } catch (Exception e) {
                LOGGER.debug("读取输入参数文本内容失败: {}", e.getMessage());
            }
        }

        return inputParameter;
    }

    @Override
    public Class<CamundaInputParameter> getModelType() {
        return CamundaInputParameter.class;
    }
}
