package com.winit.workflow.engine.core.extension.camunda.model;

import com.alibaba.smart.framework.engine.bpmn.constant.BpmnNameSpaceConstant;
import com.alibaba.smart.framework.engine.model.assembly.NoneIdBasedElement;
import lombok.Data;

import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Camunda Map 扩展元素
 * 用于解析 camunda:map 元素
 * 
 * <AUTHOR> Team
 */
@Data
public class CamundaMap implements NoneIdBasedElement {

    private static final long serialVersionUID = 1L;
    
    public final static QName qtype = new QName(BpmnNameSpaceConstant.CAMUNDA_NAME_SPACE, "map");
    
    /**
     * Map条目列表
     */
    private List<CamundaEntry> entries = new ArrayList<>();
    
    /**
     * 添加条目
     */
    public void addEntry(CamundaEntry entry) {
        this.entries.add(entry);
    }
    
    /**
     * 转换为Java Map对象
     */
    public Map<String, String> toMap() {
        Map<String, String> result = new HashMap<>();
        for (CamundaEntry entry : entries) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }
    
    /**
     * 根据key获取value
     */
    public String getValue(String key) {
        for (CamundaEntry entry : entries) {
            if (key.equals(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }
    
    /**
     * 获取所有的key
     */
    public List<String> getKeys() {
        List<String> keys = new ArrayList<>();
        for (CamundaEntry entry : entries) {
            keys.add(entry.getKey());
        }
        return keys;
    }
    
    /**
     * 判断是否为空
     */
    public boolean isEmpty() {
        return entries.isEmpty();
    }
    
    /**
     * 获取条目数量
     */
    public int size() {
        return entries.size();
    }
}
