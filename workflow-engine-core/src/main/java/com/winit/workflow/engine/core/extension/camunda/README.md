# SmartEngine Camunda BPMN扩展元素解析支持

## 概述

本模块为SmartEngine框架实现了对Camunda BPMN扩展元素的解析支持，特别是`camunda:inputOutput`相关元素，使SmartEngine能够处理Camunda格式的BPMN文件。

## 支持的扩展元素

### 1. camunda:inputOutput
- **功能**: 输入输出参数容器
- **类**: `CamundaInputOutput`
- **解析器**: `CamundaInputOutputParser`

### 2. camunda:inputParameter
- **功能**: 输入参数定义
- **类**: `CamundaInputParameter`
- **解析器**: `CamundaInputParameterParser`
- **支持类型**: 字符串值、Map值

### 3. camunda:outputParameter
- **功能**: 输出参数定义
- **类**: `CamundaOutputParameter`
- **解析器**: `CamundaOutputParameterParser`
- **支持类型**: 字符串值、Map值

### 4. camunda:map
- **功能**: 键值对映射容器
- **类**: `CamundaMap`
- **解析器**: `CamundaMapParser`

### 5. camunda:entry
- **功能**: 映射条目
- **类**: `CamundaEntry`
- **解析器**: `CamundaEntryParser`

## XML示例

```xml
<camunda:inputOutput xmlns:camunda="http://camunda.org/schema/1.0/bpmn">
  <!-- 简单字符串输入参数 -->
  <camunda:inputParameter name="Input_32b6moq">12</camunda:inputParameter>
  
  <!-- Map类型输入参数 -->
  <camunda:inputParameter name="Input_202enno">
    <camunda:map>
      <camunda:entry key="a">12</camunda:entry>
      <camunda:entry key="b">23</camunda:entry>
    </camunda:map>
  </camunda:inputParameter>
  
  <!-- 简单字符串输出参数 -->
  <camunda:outputParameter name="Output_3tmi8ob">12</camunda:outputParameter>
  
  <!-- Map类型输出参数 -->
  <camunda:outputParameter name="Output_332ovkr">
    <camunda:map>
      <camunda:entry key="aa">11</camunda:entry>
      <camunda:entry key="bb">22</camunda:entry>
    </camunda:map>
  </camunda:outputParameter>
</camunda:inputOutput>
```

## 使用方式

### 1. 编程方式创建

```java
// 创建InputOutput容器
CamundaInputOutput inputOutput = new CamundaInputOutput();

// 创建简单字符串输入参数
CamundaInputParameter input1 = new CamundaInputParameter();
input1.setName("simpleInput");
input1.setStringValue("simpleValue");

// 创建Map类型输入参数
CamundaInputParameter input2 = new CamundaInputParameter();
input2.setName("mapInput");
CamundaMap inputMap = new CamundaMap();
inputMap.addEntry(new CamundaEntry("key1", "value1"));
inputMap.addEntry(new CamundaEntry("key2", "value2"));
input2.setMapValue(inputMap);

// 添加参数
inputOutput.addInputParameter(input1);
inputOutput.addInputParameter(input2);
```

### 2. 与SmartEngine集成

```java
// 将扩展添加到ExtensionElements中
ExtensionElements extensionElements = new ExtensionElementsImpl();
ParseContext context = new ParseContext();

// 装饰扩展元素
inputOutput.decorate(extensionElements, context);

// 获取装饰后的数据
Map<String, Object> decorationMap = extensionElements.getDecorationMap();
Map<String, Object> camundaData = (Map<String, Object>) decorationMap.get(ExtensionElementsConstant.CAMUNDA_INPUT_OUTPUT);
```

## 架构设计

### 1. 模型类层次结构
- 所有模型类实现`NoneIdBasedElement`接口
- `CamundaInputOutput`实现`ExtensionDecorator`接口，支持装饰模式

### 2. 解析器层次结构
- 所有解析器继承`AbstractElementParser`基类
- 使用`@ExtensionBinding`注解进行自动注册
- 支持SmartEngine的扩展机制

### 3. 命名空间支持
- 支持Camunda命名空间：`http://camunda.org/schema/1.0/bpmn`
- 与SmartEngine现有命名空间机制兼容

## 扩展常量

在`ExtensionElementsConstant`中新增：
```java
String CAMUNDA_INPUT_OUTPUT = "CamundaInputOutput";
```

## 测试

### 1. 单元测试
- `CamundaExtensionParseTest`: 基本模型功能测试
- `CamundaExtensionUsageExample`: 使用示例和集成测试

### 2. 运行测试
```bash
cd core
mvn test -Dtest=CamundaExtensionParseTest
mvn test -Dtest=CamundaExtensionUsageExample
```

## 文件结构

```
core/src/main/java/com/alibaba/smart/framework/engine/bpmn/assembly/extension/
├── camunda/                           # Camunda模型类
│   ├── CamundaInputOutput.java
│   ├── CamundaInputParameter.java
│   ├── CamundaOutputParameter.java
│   ├── CamundaMap.java
│   └── CamundaEntry.java
└── parser/camunda/                    # Camunda解析器
    ├── CamundaInputOutputParser.java
    ├── CamundaInputParameterParser.java
    ├── CamundaOutputParameterParser.java
    ├── CamundaMapParser.java
    └── CamundaEntryParser.java
```

## 特性

1. **完整的Camunda InputOutput支持**: 支持字符串和Map类型参数
2. **类型安全**: 强类型模型类，避免运行时错误
3. **扩展性**: 基于SmartEngine扩展机制，易于扩展
4. **兼容性**: 与现有SmartEngine架构完全兼容
5. **测试覆盖**: 完整的单元测试和使用示例

## 注意事项

1. 解析器依赖SmartEngine的扩展机制，需要在完整的SmartEngine环境中使用
2. 命名空间处理依赖SmartEngine的XML解析框架
3. 扩展数据通过装饰模式存储在ExtensionElements中

## 后续扩展

可以基于此实现继续扩展支持其他Camunda元素，如：
- `camunda:executionListener`
- `camunda:taskListener`
- `camunda:properties`
- `camunda:connector`

等其他Camunda BPMN扩展元素。
