package com.winit.workflow.engine.core.extension.camunda.parser;


import com.alibaba.smart.framework.engine.exception.EngineException;
import com.alibaba.smart.framework.engine.extension.annoation.ExtensionBinding;
import com.alibaba.smart.framework.engine.extension.constant.ExtensionConstant;
import com.alibaba.smart.framework.engine.model.assembly.BaseElement;
import com.alibaba.smart.framework.engine.xml.parser.AbstractElementParser;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaEntry;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLStreamReader;

/**
 * Camunda Map 解析器
 * 用于解析 camunda:map 元素
 * 
 * <AUTHOR> Team
 */
@ExtensionBinding(group = ExtensionConstant.ELEMENT_PARSER, bindKey = CamundaMap.class)
public class CamundaMapParser extends AbstractElementParser<CamundaMap> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CamundaMapParser.class);

    @Override
    protected CamundaMap parseModel(XMLStreamReader reader, ParseContext context) {
        LOGGER.debug("开始解析 camunda:map 元素");
        return new CamundaMap();
    }

    @Override
    protected void decorateChild(CamundaMap camundaMap, BaseElement child, ParseContext context) {
        if (child instanceof CamundaEntry) {
            camundaMap.addEntry((CamundaEntry) child);
            CamundaEntry entry = (CamundaEntry) child;
            LOGGER.debug("添加Map条目: key={}, value={}", entry.getKey(), entry.getValue());
        } else {
            LOGGER.warn("未知的子元素类型: {}", child.getClass().getName());
            throw new EngineException("CamundaMap can only contain CamundaEntry child elements: " + child.getClass());
        }
    }

    @Override
    public Class<CamundaMap> getModelType() {
        return CamundaMap.class;
    }
}
