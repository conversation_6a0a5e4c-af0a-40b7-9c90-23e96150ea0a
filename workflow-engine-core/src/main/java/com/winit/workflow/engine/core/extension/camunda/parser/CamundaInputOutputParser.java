package com.winit.workflow.engine.core.extension.camunda.parser;


import com.alibaba.smart.framework.engine.exception.EngineException;
import com.alibaba.smart.framework.engine.extension.annoation.ExtensionBinding;
import com.alibaba.smart.framework.engine.extension.constant.ExtensionConstant;
import com.alibaba.smart.framework.engine.model.assembly.BaseElement;
import com.alibaba.smart.framework.engine.xml.parser.AbstractElementParser;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaInputOutput;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaInputParameter;
import com.winit.workflow.engine.core.extension.camunda.model.CamundaOutputParameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLStreamReader;

/**
 * Camunda InputOutput 解析器
 * 用于解析 camunda:inputOutput 元素
 *
 * <AUTHOR> Team
 */
@ExtensionBinding(group = ExtensionConstant.ELEMENT_PARSER, bindKey = CamundaInputOutput.class)
public class CamundaInputOutputParser extends AbstractElementParser<CamundaInputOutput> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CamundaInputOutputParser.class);

    @Override
    protected CamundaInputOutput parseModel(XMLStreamReader reader, ParseContext context) {
        LOGGER.debug("开始解析 camunda:inputOutput 元素");
        return new CamundaInputOutput();
    }

    @Override
    protected void decorateChild(CamundaInputOutput inputOutput, BaseElement child, ParseContext context) {
        if (child instanceof CamundaInputParameter) {
            inputOutput.addInputParameter((CamundaInputParameter) child);
            LOGGER.debug("添加输入参数: {}", ((CamundaInputParameter) child).getName());
        } else if (child instanceof CamundaOutputParameter) {
            inputOutput.addOutputParameter((CamundaOutputParameter) child);
            LOGGER.debug("添加输出参数: {}", ((CamundaOutputParameter) child).getName());
        } else {
            LOGGER.warn("未知的子元素类型: {}", child.getClass().getName());
            throw new EngineException("CamundaInputOutput can only contain CamundaInputParameter or CamundaOutputParameter child elements: " + child.getClass());
        }
    }

    @Override
    public Class<CamundaInputOutput> getModelType() {
        return CamundaInputOutput.class;
    }
}
