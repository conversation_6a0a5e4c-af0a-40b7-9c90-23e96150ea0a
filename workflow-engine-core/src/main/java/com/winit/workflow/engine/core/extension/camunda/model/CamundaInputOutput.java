package com.winit.workflow.engine.core.extension.camunda.model;

import com.alibaba.smart.framework.engine.bpmn.constant.BpmnNameSpaceConstant;
import com.alibaba.smart.framework.engine.common.util.MapUtil;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionDecorator;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElements;
import com.alibaba.smart.framework.engine.xml.parser.ParseContext;
import com.winit.workflow.engine.core.api.ExtensionElementsConstant;
import lombok.Data;

import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Camunda InputOutput 扩展元素
 * 用于解析 camunda:inputOutput 元素
 *
 * <AUTHOR> Team
 */
@Data
public class CamundaInputOutput implements ExtensionDecorator {

    private static final long serialVersionUID = 1L;

    public final static QName qtype = new QName(BpmnNameSpaceConstant.CAMUNDA_NAME_SPACE, "inputOutput");

    /**
     * 输入参数列表
     */
    private List<CamundaInputParameter> inputParameters = new ArrayList<>();

    /**
     * 输出参数列表
     */
    private List<CamundaOutputParameter> outputParameters = new ArrayList<>();

    @Override
    public String getDecoratorType() {
        return ExtensionElementsConstant.CAMUNDA_INPUT_OUTPUT;
    }

    @Override
    public void decorate(ExtensionElements extensionElements, ParseContext context) {
        Map<String, Object> decorationMap = extensionElements.getDecorationMap();

        // 将输入输出参数存储到装饰映射中
        Map<String, Object> camundaMap = (Map<String, Object>) decorationMap.get(getDecoratorType());
        if (camundaMap == null) {
            camundaMap = MapUtil.newHashMap();
            decorationMap.put(getDecoratorType(), camundaMap);
        }

        // 存储输入参数
        if (!inputParameters.isEmpty()) {
            Map<String, Object> inputMap = MapUtil.newHashMap();
            for (CamundaInputParameter inputParam : inputParameters) {
                inputMap.put(inputParam.getName(), inputParam.getValue());
            }
            camundaMap.put("inputParameters", inputMap);
        }

        // 存储输出参数
        if (!outputParameters.isEmpty()) {
            Map<String, Object> outputMap = MapUtil.newHashMap();
            for (CamundaOutputParameter outputParam : outputParameters) {
                outputMap.put(outputParam.getName(), outputParam.getValue());
            }
            camundaMap.put("outputParameters", outputMap);
        }
    }

    /**
     * 添加输入参数
     */
    public void addInputParameter(CamundaInputParameter inputParameter) {
        this.inputParameters.add(inputParameter);
    }

    /**
     * 添加输出参数
     */
    public void addOutputParameter(CamundaOutputParameter outputParameter) {
        this.outputParameters.add(outputParameter);
    }
}
