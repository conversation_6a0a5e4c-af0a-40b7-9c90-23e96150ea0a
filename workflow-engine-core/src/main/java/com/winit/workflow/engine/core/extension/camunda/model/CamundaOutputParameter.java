package com.winit.workflow.engine.core.extension.camunda.model;

import com.alibaba.smart.framework.engine.bpmn.constant.BpmnNameSpaceConstant;
import com.alibaba.smart.framework.engine.model.assembly.NoneIdBasedElement;
import lombok.Data;

import javax.xml.namespace.QName;

/**
 * Camunda OutputParameter 扩展元素
 * 用于解析 camunda:outputParameter 元素
 * 
 * <AUTHOR> Team
 */
@Data
public class CamundaOutputParameter implements NoneIdBasedElement {

    private static final long serialVersionUID = 1L;
    
    public final static QName qtype = new QName(BpmnNameSpaceConstant.CAMUNDA_NAME_SPACE, "outputParameter");
    
    /**
     * 参数名称
     */
    private String name;
    
    /**
     * 参数值，可以是简单字符串或复杂对象（如Map）
     */
    private Object value;
    
    /**
     * 参数类型，用于标识值的类型
     */
    private String type;
    
    /**
     * 如果值是Map类型，存储Map对象
     */
    private CamundaMap mapValue;
    
    /**
     * 设置简单字符串值
     */
    public void setStringValue(String stringValue) {
        this.value = stringValue;
        this.type = "string";
    }
    
    /**
     * 设置Map值
     */
    public void setMapValue(CamundaMap mapValue) {
        this.mapValue = mapValue;
        this.value = mapValue;
        this.type = "map";
    }
    
    /**
     * 获取字符串值
     */
    public String getStringValue() {
        if ("string".equals(type) && value instanceof String) {
            return (String) value;
        }
        return null;
    }
    
    /**
     * 判断是否为Map类型
     */
    public boolean isMapType() {
        return "map".equals(type) && mapValue != null;
    }
}
