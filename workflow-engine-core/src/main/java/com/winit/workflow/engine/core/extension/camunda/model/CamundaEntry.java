package com.winit.workflow.engine.core.extension.camunda.model;

import com.alibaba.smart.framework.engine.bpmn.constant.BpmnNameSpaceConstant;
import com.alibaba.smart.framework.engine.model.assembly.NoneIdBasedElement;
import lombok.Data;

import javax.xml.namespace.QName;

/**
 * Camunda Entry 扩展元素
 * 用于解析 camunda:entry 元素
 * 
 * <AUTHOR> Team
 */
@Data
public class CamundaEntry implements NoneIdBasedElement {

    private static final long serialVersionUID = 1L;
    
    public final static QName qtype = new QName(BpmnNameSpaceConstant.CAMUNDA_NAME_SPACE, "entry");
    
    /**
     * 条目的键
     */
    private String key;
    
    /**
     * 条目的值
     */
    private String value;
    
    /**
     * 构造函数
     */
    public CamundaEntry() {
    }
    
    /**
     * 构造函数
     */
    public CamundaEntry(String key, String value) {
        this.key = key;
        this.value = value;
    }
    
    /**
     * 判断是否有效（key和value都不为空）
     */
    public boolean isValid() {
        return key != null && !key.trim().isEmpty() && value != null;
    }
    
    @Override
    public String toString() {
        return "CamundaEntry{key='" + key + "', value='" + value + "'}";
    }
}
