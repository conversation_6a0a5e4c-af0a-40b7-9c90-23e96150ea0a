package com.winit.workflow.engine.spring.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.winit.workflow.engine.core.expression.ExpressionService;
import com.winit.workflow.engine.core.expression.FunctionInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for ExpressionController using MockMvc
 */
@WebMvcTest(controllers = ExpressionController.class)
@org.springframework.test.context.ContextConfiguration(classes = ExpressionControllerTestConfig.class)
public class ExpressionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ExpressionService expressionService;

    private ObjectMapper objectMapper;
    private List<FunctionInfo> mockFunctions;

    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();
        
        // Create mock function data
        mockFunctions = Arrays.asList(
            createMockFunction("abs", "custom", 200, 1, "Returns absolute value", "abs(number)", "${abs(-5)} → 5"),
            createMockFunction("concat", "builtin", 10, -1, "Concatenates strings", "concat(str1, str2, ...)", "${concat('Hello', ' ', 'World')} → 'Hello World'"),
            createMockFunction("max", "custom", 200, -1, "Returns maximum value", "max(num1, num2, ...)", "${max(1, 5, 3)} → 5"),
            createMockFunction("now", "builtin", 10, 0, "Returns current timestamp", "now()", "${now()} → '2025-06-20T12:00:00Z'")
        );
    }
    
    private FunctionInfo createMockFunction(String name, String type, int priority, int paramCount, 
                                          String description, String signature, String example) {
        FunctionInfo function = new FunctionInfo();
        function.setFunctionName(name);
        function.setType(type);
        function.setPriority(priority);
        function.setParameterCount(paramCount);
        function.setDescription(description);
        function.setFunctionSignature(signature);
        function.setExample(example);
        return function;
    }
    
    @Test
    public void testGetAllFunctions() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(4))
                .andExpect(jsonPath("$[0].functionName").value("abs"))
                .andExpect(jsonPath("$[0].type").value("custom"))
                .andExpect(jsonPath("$[0].priority").value(200))
                .andExpect(jsonPath("$[1].functionName").value("concat"))
                .andExpect(jsonPath("$[1].type").value("builtin"))
                .andExpect(jsonPath("$[1].priority").value(10));
    }
    
    @Test
    public void testGetFunctionByName() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions/abs")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.functionName").value("abs"))
                .andExpect(jsonPath("$.type").value("custom"))
                .andExpect(jsonPath("$.description").value("Returns absolute value"));
    }
    
    @Test
    public void testGetFunctionByNameNotFound() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions/nonexistent")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }
    
    @Test
    public void testGetFunctionsByTypeBuiltin() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions/type/builtin")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].type").value("builtin"))
                .andExpect(jsonPath("$[1].type").value("builtin"));
    }
    
    @Test
    public void testGetFunctionsByTypeCustom() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions/type/custom")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].type").value("custom"))
                .andExpect(jsonPath("$[1].type").value("custom"));
    }
    
    @Test
    public void testGetFunctionsByTypeInvalid() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions/type/invalid")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    public void testHealthEndpoint() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/health")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("healthy")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("4 functions available")));
    }
    
    @Test
    public void testHealthEndpointServiceError() throws Exception {
        when(expressionService.getAllFunctions()).thenThrow(new RuntimeException("Service error"));
        
        mockMvc.perform(get("/api/v1/expression/health")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isServiceUnavailable())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("not available")));
    }
    
    @Test
    public void testCorsHeaders() throws Exception {
        when(expressionService.getAllFunctions()).thenReturn(mockFunctions);
        
        mockMvc.perform(get("/api/v1/expression/functions")
                .header("Origin", "http://localhost:3000")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "*"));
    }
}
